import { auth } from '@/firebase/firebaseConfig';

/**
 * Service for generating onboarding recipes via Firebase Cloud Functions
 *
 * @deprecated This service is no longer used. Recipe generation is now triggered automatically
 * when a dietPreferences document is created in Firestore via a Firestore trigger function.
 */
export class OnboardingRecipeService {
  private static readonly CLOUD_FUNCTION_URL = 'https://us-central1-chefpal-a9abe.cloudfunctions.net/generate_recipes';

  /**
   * Triggers the generation of 30 recipes (10 breakfast, 10 lunch, 10 dinner)
   * based on the user's diet preferences after onboarding completion.
   * The recipes will be stored in Firestore under the "recipes" collection
   * with the user's UID as the document ID.
   *
   * @returns Promise that resolves when recipes are successfully generated and stored
   * @throws Error if the request fails or user is not authenticated
   */
  static async generateOnboardingRecipes(): Promise<{
    success: boolean;
    message: string;
    recipeCount: number;
  }> {
    try {
      // Get the current user's ID token for authentication
      const idToken = await auth.currentUser?.getIdToken();

      if (!idToken) {
        throw new Error('User not authenticated. Please sign in first.');
      }

      const response = await fetch(this.CLOUD_FUNCTION_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${idToken}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `HTTP error! status: ${response.status}`
        );
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to generate recipes');
      }

      return result;
    } catch (error) {
      console.error('Error generating onboarding recipes:', error);
      throw error;
    }
  }
}
