import { firestoreRepository, FirestoreCollections } from '@/repositories/firestoreRepository';
import { Recipe } from '@/app/components/types';
import { auth } from '@/firebase/firebaseConfig';

/**
 * Service for managing user recipes stored in Firestore
 */
export class RecipeService {
  /**
   * Retrieves the user's generated recipes from Firestore
   *
   * @returns Promise that resolves to the user's recipes or null if not found
   * @throws Error if user is not authenticated or if there's a database error
   */
  static async getUserRecipes(): Promise<{
    recipes: Recipe[];
    count: number;
    generatedAt: any;
    lastUpdated?: any;
    dietPreferences: any;
  } | null> {
    try {
      const user = auth.currentUser;
      if (!user) {
        throw new Error('User not authenticated');
      }

      const recipesDoc = await firestoreRepository.getDocument(
        FirestoreCollections.RECIPES,
        user.uid
      );

      if (!recipesDoc) {
        return null;
      }

      return {
        recipes: recipesDoc.recipes || [],
        count: recipesDoc.count || 0,
        generatedAt: recipesDoc.generatedAt,
        lastUpdated: recipesDoc.lastUpdated,
        dietPreferences: recipesDoc.dietPreferences
      };
    } catch (error) {
      console.error('Error retrieving user recipes:', error);
      throw error;
    }
  }

  /**
   * Checks if the user has generated recipes
   *
   * @returns Promise that resolves to true if recipes exist, false otherwise
   */
  static async hasUserRecipes(): Promise<boolean> {
    try {
      const recipesData = await this.getUserRecipes();
      return recipesData !== null && recipesData.recipes.length > 0;
    } catch (error) {
      console.error('Error checking if user has recipes:', error);
      return false;
    }
  }

  /**
   * Gets the total count of user's recipes
   *
   * @returns Promise that resolves to the total recipe count
   */
  static async getUserRecipeCount(): Promise<number> {
    try {
      const recipesData = await this.getUserRecipes();
      return recipesData?.count || 0;
    } catch (error) {
      console.error('Error getting user recipe count:', error);
      return 0;
    }
  }

  /**
   * Filters recipes by meal type
   *
   * @param recipes Array of recipes to filter
   * @param mealType The meal type to filter by
   * @returns Filtered array of recipes
   */
  static filterRecipesByMealType(recipes: Recipe[], mealType: string): Recipe[] {
    return recipes.filter(recipe => recipe.mealType === mealType);
  }

  /**
   * Gets recipes grouped by meal type
   *
   * @returns Promise that resolves to recipes grouped by meal type
   */
  static async getRecipesByMealType(): Promise<{
    breakfast: Recipe[];
    lunch: Recipe[];
    dinner: Recipe[];
    snacks: Recipe[];
    dessert: Recipe[];
  } | null> {
    try {
      const recipesData = await this.getUserRecipes();

      if (!recipesData) {
        return null;
      }

      const recipes = recipesData.recipes;

      return {
        breakfast: this.filterRecipesByMealType(recipes, 'Breakfast'),
        lunch: this.filterRecipesByMealType(recipes, 'Lunch'),
        dinner: this.filterRecipesByMealType(recipes, 'Dinner'),
        snacks: this.filterRecipesByMealType(recipes, 'Snacks'),
        dessert: this.filterRecipesByMealType(recipes, 'Dessert')
      };
    } catch (error) {
      console.error('Error getting recipes by meal type:', error);
      throw error;
    }
  }
}
